{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "0J5HwZWRoGPI9X1HyBo5I", "sessionId": "SjaVOMWreqOUNywMojysO", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.15", "cliVersion": "9.0.15"}}, "timestamp": 1751513868251}, "upgrade": {"body": {"eventType": "upgrade", "eventId": "94ukN8QM2VtMsHESmTvf5", "sessionId": "SjaVOMWreqOUNywMojysO", "metadata": {"generatedAt": 1751511761474, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "monorepo": "Workspaces", "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.15", "language": "typescript", "storybookPackages": {"@storybook/vue3-vite": {"version": "9.0.15"}, "storybook": {"version": "9.0.15"}, "@storybook/addon-docs": {"version": "9.0.15"}}, "addons": {"@storybook/addon-links": {"version": "9.0.15"}}}, "payload": {"beforeVersion": "8.6.14", "afterVersion": "9.0.15", "automigrationResults": {"eslintPlugin": "unnecessary", "addon-mdx-gfm-remove": "unnecessary", "addon-storysource-code-panel": "unnecessary", "upgrade-storybook-related-dependencies": "unnecessary", "initial-globals": "unnecessary", "addon-a11y-addon-test": "unnecessary", "consolidated-imports": "succeeded", "addon-experimental-test": "unnecessary", "rnstorybook-config": "unnecessary", "remove-addon-interactions": "succeeded", "renderer-to-framework": "succeeded", "remove-essential-addons": "succeeded", "addon-a11y-parameters": "unnecessary", "remove-docs-autodocs": "succeeded", "wrap-require": "succeeded"}, "automigrationFailureCount": 0, "automigrationPreCheckFailure": ["dependenciesVersions", "minimumNode20", "dependenciesVersions", "major-version-gap", "experimentalAddonTestVitest"], "doctorResults": {"missing_storybook_dependency": "passed", "incompatible_packages": "passed", "mismatching_versions": "passed", "duplicated_dependencies": "passed", "configuration_error": "passed"}, "doctorFailureCount": 0, "doctorErrorCount": 0}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.15", "cliVersion": "9.0.15"}}, "timestamp": 1751511762453}, "multi-upgrade": {"body": {"eventType": "multi-upgrade", "eventId": "qZA0WPAtqQYolrEZUfKQs", "sessionId": "SjaVOMWreqOUNywMojysO", "metadata": {"generatedAt": 1751511761474, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "monorepo": "Workspaces", "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.15", "language": "typescript", "storybookPackages": {"@storybook/vue3-vite": {"version": "9.0.15"}, "storybook": {"version": "9.0.15"}, "@storybook/addon-docs": {"version": "9.0.15"}}, "addons": {"@storybook/addon-links": {"version": "9.0.15"}}}, "payload": {"totalDetectedProjects": 1, "totalSelectedProjects": 1, "projectsWithSuccessfulAutomigrations": 1, "projectsWithFailedAutomigrations": 0, "projectsWithNoAutomigrations": 0, "projectsWithDoctorReports": 0, "incompleteProjects": 0, "hasUserInterrupted": false}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.15", "cliVersion": "9.0.15"}}, "timestamp": 1751511763044}, "version-update": {"body": {"eventType": "version-update", "eventId": "Vd3gg6ISrsmmL2U8dolC4", "sessionId": "SjaVOMWreqOUNywMojysO", "metadata": {"generatedAt": 1751511837523, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "monorepo": "Workspaces", "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.15", "language": "typescript", "storybookPackages": {"@storybook/vue3-vite": {"version": "9.0.15"}, "storybook": {"version": "9.0.15"}}, "addons": {"@storybook/addon-links": {"version": "9.0.15"}, "@storybook/addon-docs": {"version": "9.0.15"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.15", "cliVersion": "9.0.15"}}, "timestamp": 1751511838440}, "dev": {"body": {"eventType": "dev", "eventId": "7F3gUqwM1uGGTD6zsmoSV", "sessionId": "SjaVOMWreqOUNywMojysO", "metadata": {"generatedAt": 1751513867903, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "monorepo": "Workspaces", "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.15", "storybookVersionSpecifier": "^9.0.15", "language": "typescript", "storybookPackages": {"@storybook/vue3": {"version": "9.0.15"}, "@storybook/vue3-vite": {"version": "9.0.15"}, "storybook": {"version": "9.0.15"}}, "addons": {"@storybook/addon-links": {"version": "9.0.15"}, "@storybook/addon-docs": {"version": "9.0.15"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 8, "componentCount": 1, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 1, "mdxCount": 0, "exampleStoryCount": 0, "exampleDocsCount": 0, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 1, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 0, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "storybookVersion": "9.0.15", "cliVersion": "9.0.15"}}, "timestamp": 1751513868947}}}