try{
(() => {
  // global-externals:storybook/manager-api
  var manager_api_default = __STORYBOOK_API__, { ActiveTabs, Consumer, ManagerContext, Provider, RequestResponseError, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, experimental_MockUniversalStore, experimental_UniversalStore, experimental_getStatusStore, experimental_getTestProviderStore, experimental_requestResponse, experimental_useStatusStore, experimental_useTestProviderStore, experimental_useUniversalStore, internal_fullStatusStore, internal_fullTestProviderStore, internal_universalStatusStore, internal_universalTestProviderStore, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOK_API__;

  // ../../node_modules/.pnpm/@storybook+addon-links@9.0.15_react@19.1.0_storybook@9.0.15/node_modules/@storybook/addon-links/dist/manager.js
  var ADDON_ID = "storybook/links", constants_default = { NAVIGATE: `${ADDON_ID}/navigate`, REQUEST: `${ADDON_ID}/request`, RECEIVE: `${ADDON_ID}/receive` };
  addons.register(ADDON_ID, (api) => {
    api.on(constants_default.REQUEST, ({ kind, name }) => {
      let id = api.storyId(kind, name);
      api.emit(constants_default.RECEIVE, id);
    });
  });
})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
