@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\dist\esm\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\dist\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\dist\esm\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\dist\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules\rimraf\node_modules;E:\code\work\components\node_modules\.pnpm\rimraf@5.0.10\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\rimraf\dist\esm\bin.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\rimraf\dist\esm\bin.mjs" %*
)
