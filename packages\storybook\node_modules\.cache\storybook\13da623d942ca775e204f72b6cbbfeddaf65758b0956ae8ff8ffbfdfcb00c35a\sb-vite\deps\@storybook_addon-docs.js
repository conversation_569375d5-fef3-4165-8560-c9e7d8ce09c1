import {
  <PERSON>s<PERSON>enderer
} from "./chunk-QCHHIR47.js";
import "./chunk-A5SBGBTH.js";
import "./chunk-KHXBKA4L.js";
import {
  __export
} from "./chunk-JKIPRDOA.js";
import "./chunk-UCJOPCEI.js";
import "./chunk-5HJQXSS2.js";
import "./chunk-QFCKH4KW.js";
import "./chunk-T2RKIFGW.js";
import "./chunk-7LJ2A7BE.js";
import "./chunk-3XQW2NJB.js";
import "./chunk-LGCSI5EO.js";
import {
  require_preview_api
} from "./chunk-EA2IITB3.js";
import {
  __toESM
} from "./chunk-PR4QN5HX.js";

// ../../node_modules/.pnpm/@storybook+addon-docs@9.0.15_@types+react@19.1.8_storybook@9.0.15/node_modules/@storybook/addon-docs/dist/index.mjs
var import_preview_api = __toESM(require_preview_api(), 1);
var preview_exports = {};
__export(preview_exports, { parameters: () => parameters });
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer: DocsRenderer2 } = await import("./DocsRenderer-3PZUHFFL-ZJCUJWMC.js");
  return new DocsRenderer2();
}, stories: { filter: (story) => (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !story.parameters.docs?.disable } } };
var index_default = () => (0, import_preview_api.definePreview)(preview_exports);
export {
  DocsRenderer,
  index_default as default
};
//# sourceMappingURL=@storybook_addon-docs.js.map
