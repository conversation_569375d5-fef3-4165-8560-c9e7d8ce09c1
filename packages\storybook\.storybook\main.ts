import type { StorybookConfig } from "@storybook/vue3-vite";
import { resolve } from "path";
import vue from "@vitejs/plugin-vue";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: ["@storybook/addon-links", "@storybook/addon-docs"],
  framework: {
    name: "@storybook/vue3-vite",
    options: {},
  },
  typescript: {
    check: false,
  },
  viteFinal: async (config) => {
    // 确保能正确解析 workspace 依赖
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        "@components/table": resolve(__dirname, "../../table/src"),
        "@components/table/src": resolve(__dirname, "../../table/src"),
      };
    }

    // 确保 Vue 插件已加载
    if (config.plugins) {
      // 检查是否已经有 Vue 插件
      const hasVuePlugin = config.plugins.some((plugin: any) => {
        return plugin && plugin.name === "vite:vue";
      });

      if (!hasVuePlugin) {
        config.plugins.push(vue());
      }
    } else {
      config.plugins = [vue()];
    }

    return config;
  },
};

export default config;
