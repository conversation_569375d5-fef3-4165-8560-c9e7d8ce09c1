import type { StorybookConfig } from "@storybook/vue3-vite";
import { resolve } from "path";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: ["@storybook/addon-links", "@storybook/addon-docs"],
  framework: {
    name: "@storybook/vue3-vite",
    options: {},
  },
  typescript: {
    check: false,
  },
  viteFinal: async (config) => {
    // 确保能正确解析 workspace 依赖
    if (!config.resolve) {
      config.resolve = {};
    }

    config.resolve.alias = {
      ...config.resolve.alias,
      "@components/table": resolve(__dirname, "../../table/src"),
    };

    // 确保 Vue 和 JSX 插件已加载
    if (!config.plugins) {
      config.plugins = [];
    }

    // 检查是否已经有 Vue 插件
    const hasVuePlugin = config.plugins.some((plugin: any) => {
      return plugin && plugin.name === "vite:vue";
    });

    const hasVueJsxPlugin = config.plugins.some((plugin: any) => {
      return plugin && plugin.name === "vite:vue-jsx";
    });

    if (!hasVuePlugin) {
      config.plugins.push(vue());
    }

    if (!hasVueJsxPlugin) {
      config.plugins.push(vueJsx());
    }

    return config;
  },
};

export default config;
