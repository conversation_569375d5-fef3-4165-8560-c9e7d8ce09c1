import type { StorybookConfig } from "@storybook/vue3-vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: ["@storybook/addon-links", "@storybook/addon-docs"],
  framework: {
    name: "@storybook/vue3-vite",
    options: {},
  },
  typescript: {
    check: false,
  },
  viteFinal: async (config) => {
    const path = require("path");
    const vue = require("@vitejs/plugin-vue");

    // 确保能正确解析 workspace 依赖
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        "@components/table": path.resolve(__dirname, "../../table/src"),
        "@components/table/src": path.resolve(__dirname, "../../table/src"),
      };
    }

    // 确保 Vue 插件已加载
    if (config.plugins) {
      const hasVuePlugin = config.plugins.some(
        (plugin) => plugin && typeof plugin === "object"
      );
      if (!hasVuePlugin) {
        config.plugins.push(vue());
      }
    }

    return config;
  },
};

export default config;
