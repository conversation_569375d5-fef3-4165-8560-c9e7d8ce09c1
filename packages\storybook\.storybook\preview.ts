import type { Preview } from "@storybook/vue3";
import { setup } from "@storybook/vue3";

// 导入 table 库的样式
// import "@components/table/src/style.css";

// 设置 Vue 应用
setup((app) => {
  // 这里可以添加全局插件、组件等
});

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      toc: true,
    },
  },
  tags: ["autodocs"],
};

export default preview;
