@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules\@playwright\test\node_modules;E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules\@playwright\node_modules;E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules\@playwright\test\node_modules;E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules\@playwright\node_modules;E:\code\work\components\node_modules\.pnpm\@playwright+test@1.53.2\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@playwright\test\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@playwright\test\cli.js" %*
)
