#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules/@playwright/test/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules/@playwright/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules/@playwright/test/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules/@playwright/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/@playwright+test@1.53.2/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@playwright/test/cli.js" "$@"
else
  exec node  "$basedir/../@playwright/test/cli.js" "$@"
fi
