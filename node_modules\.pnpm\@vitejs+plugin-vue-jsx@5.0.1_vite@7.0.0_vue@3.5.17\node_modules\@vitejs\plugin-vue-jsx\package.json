{"name": "@vitejs/plugin-vue-jsx", "version": "5.0.1", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Provides Vue 3 JSX & TSX support with HMR.", "keywords": ["vite", "vite-plugin", "vue"], "files": ["dist"], "exports": {".": "./dist/index.mjs", "./package.json": "./package.json"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue.git", "directory": "packages/plugin-vue-jsx"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue-jsx#readme", "dependencies": {"@babel/core": "^7.27.7", "@babel/plugin-transform-typescript": "^7.27.1", "@rolldown/pluginutils": "^1.0.0-beta.21", "@vue/babel-plugin-jsx": "^1.4.0"}, "devDependencies": {"vite": "^6.3.5"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0 || ^7.0.0", "vue": "^3.0.0"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild"}}