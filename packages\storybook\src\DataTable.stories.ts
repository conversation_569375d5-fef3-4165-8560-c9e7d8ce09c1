import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import { DataTable } from "@components/table";
import type { TableColumn, TableData } from "@components/table";

// 示例数据
const sampleColumns: TableColumn[] = [
  {
    key: "id",
    title: "ID",
    width: 80,
    sortable: true,
  },
  {
    key: "name",
    title: "姓名",
    width: 120,
    sortable: true,
  },
  {
    key: "age",
    title: "年龄",
    width: 80,
    align: "center",
    sortable: true,
  },
  {
    key: "email",
    title: "邮箱",
    width: 200,
  },
  {
    key: "department",
    title: "部门",
    width: 120,
  },
  {
    key: "status",
    title: "状态",
    width: 100,
    align: "center",
    render: (value: string) => {
      const statusMap: Record<string, { text: string; class: string }> = {
        active: {
          text: "活跃",
          class: "bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs",
        },
        inactive: {
          text: "非活跃",
          class: "bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs",
        },
        pending: {
          text: "待审核",
          class: "bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs",
        },
      };
      const status = statusMap[value] || { text: value, class: "" };
      return `<span class="${status.class}">${status.text}</span>`;
    },
  },
];

const sampleData: TableData[] = [
  {
    id: 1,
    name: "张三",
    age: 28,
    email: "<EMAIL>",
    department: "技术部",
    status: "active",
  },
  {
    id: 2,
    name: "李四",
    age: 32,
    email: "<EMAIL>",
    department: "产品部",
    status: "active",
  },
  {
    id: 3,
    name: "王五",
    age: 25,
    email: "<EMAIL>",
    department: "设计部",
    status: "pending",
  },
  {
    id: 4,
    name: "赵六",
    age: 30,
    email: "<EMAIL>",
    department: "运营部",
    status: "inactive",
  },
  {
    id: 5,
    name: "钱七",
    age: 27,
    email: "<EMAIL>",
    department: "技术部",
    status: "active",
  },
];

const meta: Meta<typeof DataTable> = {
  title: "Components/DataTable",
  component: DataTable,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "一个功能丰富的数据表格组件，支持排序、分页、自定义渲染等功能。",
      },
    },
  },
  argTypes: {
    columns: {
      description: "表格列配置",
      control: { type: "object" },
    },
    data: {
      description: "表格数据",
      control: { type: "object" },
    },
    loading: {
      description: "加载状态",
      control: { type: "boolean" },
    },
    pagination: {
      description: "是否启用分页",
      control: { type: "boolean" },
    },
    pageSize: {
      description: "每页显示条数",
      control: { type: "number" },
    },
    showHeader: {
      description: "是否显示表头",
      control: { type: "boolean" },
    },
    bordered: {
      description: "是否显示边框",
      control: { type: "boolean" },
    },
    striped: {
      description: "是否显示斑马纹",
      control: { type: "boolean" },
    },
    hoverable: {
      description: "是否启用悬停效果",
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础表格
export const Default: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    showHeader: true,
    bordered: true,
    hoverable: true,
  },
};

// 加载状态
export const Loading: Story = {
  args: {
    columns: sampleColumns,
    data: [],
    loading: true,
  },
};

// 空数据
export const Empty: Story = {
  args: {
    columns: sampleColumns,
    data: [],
    loading: false,
  },
};

// 分页表格
export const WithPagination: Story = {
  args: {
    columns: sampleColumns,
    data: Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `用户${i + 1}`,
      age: 20 + (i % 30),
      email: `user${i + 1}@example.com`,
      department: ["技术部", "产品部", "设计部", "运营部"][i % 4],
      status: ["active", "inactive", "pending"][i % 3],
    })),
    pagination: true,
    pageSize: 5,
  },
};

// 斑马纹表格
export const Striped: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    striped: true,
    bordered: false,
  },
};

// 无边框表格
export const NoBorder: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    bordered: false,
    hoverable: true,
  },
};

// 隐藏表头
export const NoHeader: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    showHeader: false,
  },
};
