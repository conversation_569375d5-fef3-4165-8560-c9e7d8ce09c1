import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3";
import { DataTable } from "@components/table";
import type { TableColumn, TableData } from "@components/table";
import { ref } from "vue";

// 示例数据
const sampleColumns: TableColumn[] = [
  {
    key: "id",
    title: "ID",
    width: 80,
    sortable: true,
  },
  {
    key: "name",
    title: "姓名",
    width: 120,
    sortable: true,
  },
  {
    key: "age",
    title: "年龄",
    width: 80,
    align: "center",
    sortable: true,
  },
  {
    key: "email",
    title: "邮箱",
    width: 200,
  },
  {
    key: "department",
    title: "部门",
    width: 120,
  },
  {
    key: "status",
    title: "状态",
    width: 100,
    align: "center",
    render: (value: string) => {
      const statusMap: Record<string, { text: string; class: string }> = {
        active: {
          text: "活跃",
          class: "bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs",
        },
        inactive: {
          text: "非活跃",
          class: "bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs",
        },
        pending: {
          text: "待审核",
          class: "bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs",
        },
      };
      const status = statusMap[value] || { text: value, class: "" };
      return h("span", { class: status.class }, status.text);
    },
  },
];

const sampleData: TableData[] = [
  {
    id: 1,
    name: "张三",
    age: 28,
    email: "<EMAIL>",
    department: "技术部",
    status: "active",
  },
  {
    id: 2,
    name: "李四",
    age: 32,
    email: "<EMAIL>",
    department: "产品部",
    status: "active",
  },
  {
    id: 3,
    name: "王五",
    age: 25,
    email: "<EMAIL>",
    department: "设计部",
    status: "pending",
  },
  {
    id: 4,
    name: "赵六",
    age: 30,
    email: "<EMAIL>",
    department: "运营部",
    status: "inactive",
  },
  {
    id: 5,
    name: "钱七",
    age: 27,
    email: "<EMAIL>",
    department: "技术部",
    status: "active",
  },
];

const meta: Meta<typeof DataTable> = {
  title: "Components/DataTable",
  component: DataTable,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "一个功能丰富的数据表格组件，支持排序、分页、自定义渲染等功能。",
      },
    },
  },
  argTypes: {
    columns: {
      description: "表格列配置",
      control: { type: "object" },
    },
    data: {
      description: "表格数据",
      control: { type: "object" },
    },
    loading: {
      description: "加载状态",
      control: { type: "boolean" },
    },
    pagination: {
      description: "是否启用分页",
      control: { type: "boolean" },
    },
    pageSize: {
      description: "每页显示条数",
      control: { type: "number" },
    },
    showHeader: {
      description: "是否显示表头",
      control: { type: "boolean" },
    },
    bordered: {
      description: "是否显示边框",
      control: { type: "boolean" },
    },
    striped: {
      description: "是否显示斑马纹",
      control: { type: "boolean" },
    },
    hoverable: {
      description: "是否启用悬停效果",
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础表格
export const Default: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    showHeader: true,
    bordered: true,
    hoverable: true,
  },
};

// 加载状态
export const Loading: Story = {
  args: {
    columns: sampleColumns,
    data: [],
    loading: true,
  },
};

// 空数据
export const Empty: Story = {
  args: {
    columns: sampleColumns,
    data: [],
    loading: false,
  },
};

// 分页表格
export const WithPagination: Story = {
  args: {
    columns: sampleColumns,
    data: Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `用户${i + 1}`,
      age: 20 + (i % 30),
      email: `user${i + 1}@example.com`,
      department: ["技术部", "产品部", "设计部", "运营部"][i % 4],
      status: ["active", "inactive", "pending"][i % 3],
    })),
    pagination: true,
    pageSize: 5,
  },
};

// 斑马纹表格
export const Striped: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    striped: true,
    bordered: false,
  },
};

// 无边框表格
export const NoBorder: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    bordered: false,
    hoverable: true,
  },
};

// 隐藏表头
export const NoHeader: Story = {
  args: {
    columns: sampleColumns,
    data: sampleData,
    showHeader: false,
  },
};

// 使用 Vue3 render 函数的示例
export const WithRenderFunction: Story = {
  render: (args) => {
    // 创建响应式数据
    const tableData = ref([...sampleData]);
    const loading = ref(false);

    // 自定义列配置，包含操作按钮
    const customColumns: TableColumn[] = [
      ...sampleColumns.slice(0, -1), // 排除最后一列状态列
      {
        key: "status",
        title: "状态",
        width: 100,
        align: "center",
        render: (value: string) => {
          const statusMap: Record<string, { text: string; class: string }> = {
            active: {
              text: "活跃",
              class:
                "bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs",
            },
            inactive: {
              text: "非活跃",
              class: "bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs",
            },
            pending: {
              text: "待审核",
              class:
                "bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs",
            },
          };
          const status = statusMap[value] || { text: value, class: "" };
          return h("span", { class: status.class }, status.text);
        },
      },
      {
        key: "actions",
        title: "操作",
        width: 150,
        align: "center",
        render: (_, record: TableData) => {
          return h("div", { class: "flex gap-2 justify-center" }, [
            h(
              "button",
              {
                class:
                  "px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",
                onClick: () => {
                  alert(`编辑用户: ${record.name}`);
                },
              },
              "编辑"
            ),
            h(
              "button",
              {
                class:
                  "px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors",
                onClick: () => {
                  if (confirm(`确定要删除用户 ${record.name} 吗？`)) {
                    const index = tableData.value.findIndex(
                      (item) => item.id === record.id
                    );
                    if (index > -1) {
                      tableData.value.splice(index, 1);
                    }
                  }
                },
              },
              "删除"
            ),
          ]);
        },
      },
    ];

    // 添加新用户的函数
    const addUser = () => {
      const newId = Math.max(...tableData.value.map((item) => item.id)) + 1;
      const newUser = {
        id: newId,
        name: `新用户${newId}`,
        age: 25,
        email: `newuser${newId}@example.com`,
        department: "技术部",
        status: "active",
      };
      tableData.value.push(newUser);
    };

    // 刷新数据的函数
    const refreshData = async () => {
      loading.value = true;
      // 模拟异步加载
      await new Promise((resolve) => setTimeout(resolve, 1000));
      tableData.value = [...sampleData];
      loading.value = false;
    };

    // 使用 h 函数创建 VNode
    return h("div", { class: "space-y-4" }, [
      // 操作按钮区域
      h("div", { class: "flex gap-2" }, [
        h(
          "button",
          {
            class:
              "px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors",
            onClick: addUser,
          },
          "添加用户"
        ),
        h(
          "button",
          {
            class:
              "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",
            onClick: refreshData,
          },
          "刷新数据"
        ),
      ]),
      // 数据表格
      h(DataTable, {
        ...args,
        columns: customColumns,
        data: tableData.value,
        loading: loading.value,
        bordered: true,
        hoverable: true,
      }),
    ]);
  },
  args: {
    // 这些参数会被 render 函数中的配置覆盖
    columns: sampleColumns,
    data: sampleData,
  },
  parameters: {
    docs: {
      description: {
        story: `
这个示例展示了如何使用 Vue3 的 render 函数来创建更复杂的交互式表格：

- 使用 \`h()\` 函数创建 VNode
- 使用 \`ref()\` 创建响应式数据
- 在列配置中使用 render 函数创建操作按钮
- 实现添加、删除、刷新等交互功能
- 展示了如何在 Storybook 中使用 render 函数

**主要特性：**
- ✅ 响应式数据更新
- ✅ 自定义操作按钮
- ✅ 交互式用户操作
- ✅ 异步数据加载
        `,
      },
    },
  },
};
