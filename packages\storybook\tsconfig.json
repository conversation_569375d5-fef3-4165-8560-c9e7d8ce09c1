{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/**/*", ".storybook/**/*", "../table/src/**/*"], "exclude": ["node_modules", "storybook-static"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "jsx": "preserve", "paths": {"@components/table": ["../table/src"], "@components/table/*": ["../table/src/*"]}}}