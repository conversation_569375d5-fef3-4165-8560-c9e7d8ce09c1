{"version": 3, "sources": ["../../../../../../../../node_modules/.pnpm/@storybook+addon-links@9.0.15_react@19.1.0_storybook@9.0.15/node_modules/@storybook/addon-links/dist/preview.mjs"], "sourcesContent": ["import { makeDecorator, addons } from 'storybook/preview-api';\nimport { STORY_CHANGED, SELECT_STORY } from 'storybook/internal/core-events';\nimport 'storybook/internal/csf';\nimport { global } from '@storybook/global';\n\nvar PARAM_KEY=\"links\";var{document,HTMLElement}=global;var navigate=params=>addons.getChannel().emit(SELECT_STORY,params);var linksListener=e=>{let{target}=e;if(!(target instanceof HTMLElement))return;let element=target,{sbKind:kind,sbStory:story}=element.dataset;(kind||story)&&(e.preventDefault(),navigate({kind,story}));},hasListener=!1,on=()=>{hasListener||(hasListener=!0,document.addEventListener(\"click\",linksListener));},off=()=>{hasListener&&(hasListener=!1,document.removeEventListener(\"click\",linksListener));},withLinks=makeDecorator({name:\"withLinks\",parameterName:PARAM_KEY,wrapper:(getStory,context)=>(on(),addons.getChannel().once(STORY_CHANGED,off),getStory(context))});var decorators=[withLinks];\n\nexport { decorators };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,yBAAsC;AACtC,yBAA4C;AAE5C,oBAAuB;AAEvB,IAAI,YAAU;AAAQ,IAAG,EAAC,UAAS,YAAW,IAAE;AAAO,IAAI,WAAS,YAAQ,0BAAO,WAAW,EAAE,KAAK,iCAAa,MAAM;AAAE,IAAI,gBAAc,OAAG;AAAC,MAAG,EAAC,OAAM,IAAE;AAAE,MAAG,EAAE,kBAAkB,aAAa;AAAO,MAAI,UAAQ,QAAO,EAAC,QAAO,MAAK,SAAQ,MAAK,IAAE,QAAQ;AAAQ,GAAC,QAAM,WAAS,EAAE,eAAe,GAAE,SAAS,EAAC,MAAK,MAAK,CAAC;AAAG;AAAzM,IAA2M,cAAY;AAAvN,IAA0N,KAAG,MAAI;AAAC,kBAAc,cAAY,MAAG,SAAS,iBAAiB,SAAQ,aAAa;AAAG;AAAjT,IAAmT,MAAI,MAAI;AAAC,kBAAc,cAAY,OAAG,SAAS,oBAAoB,SAAQ,aAAa;AAAG;AAA9Y,IAAgZ,gBAAU,kCAAc,EAAC,MAAK,aAAY,eAAc,WAAU,SAAQ,CAAC,UAAS,aAAW,GAAG,GAAE,0BAAO,WAAW,EAAE,KAAK,kCAAc,GAAG,GAAE,SAAS,OAAO,GAAE,CAAC;AAAE,IAAI,aAAW,CAAC,SAAS;", "names": []}