#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/bin/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/bin/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/bin/index.cjs" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/storybook@9.0.15_@testing-library+dom@10.4.0/node_modules/storybook/bin/index.cjs" "$@"
fi
