#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/is-docker@2.2.1/node_modules/is-docker/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/is-docker@2.2.1/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/is-docker@2.2.1/node_modules/is-docker/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/is-docker@2.2.1/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../is-docker/cli.js" "$@"
else
  exec node  "$basedir/../is-docker/cli.js" "$@"
fi
