import {
  require_global
} from "./chunk-7LJ2A7BE.js";
import {
  entry_preview_exports,
  renderToCanvas,
  setup
} from "./chunk-YJZSCVBE.js";
import "./chunk-AF3WTT5H.js";
import {
  h
} from "./chunk-N7SOGT2A.js";
import "./chunk-3XQW2NJB.js";
import "./chunk-LGCSI5EO.js";
import {
  require_preview_api
} from "./chunk-EA2IITB3.js";
import {
  __toESM
} from "./chunk-PR4QN5HX.js";

// ../../node_modules/.pnpm/@storybook+vue3@9.0.15_storybook@9.0.15_vue@3.5.17/node_modules/@storybook/vue3/dist/index.mjs
var import_global = __toESM(require_global(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var { window: globalWindow } = import_global.global;
globalWindow.STORYBOOK_ENV = "vue3";
globalWindow.PLUGINS_SETUP_FUNCTIONS ||= /* @__PURE__ */ new Set();
function setProjectAnnotations(projectAnnotations) {
  return (0, import_preview_api.setDefaultProjectAnnotations)(vueProjectAnnotations), (0, import_preview_api.setProjectAnnotations)(projectAnnotations);
}
var vueProjectAnnotations = { ...entry_preview_exports, renderToCanvas: (renderContext, canvasElement) => {
  if (renderContext.storyContext.testingLibraryRender == null) return renderToCanvas(renderContext, canvasElement);
  let { storyFn, storyContext: { testingLibraryRender: render } } = renderContext, { unmount } = render(storyFn(), { container: canvasElement });
  return unmount;
} };
function composeStory(story, componentAnnotations, projectAnnotations, exportsName) {
  let composedStory = (0, import_preview_api.composeStory)(story, componentAnnotations, projectAnnotations, globalThis.globalProjectAnnotations ?? vueProjectAnnotations, exportsName), renderable = (...args) => h(composedStory(...args));
  return Object.assign(renderable, composedStory), renderable;
}
function composeStories(csfExports, projectAnnotations) {
  return (0, import_preview_api.composeStories)(csfExports, projectAnnotations, composeStory);
}
try {
  module?.hot?.decline && module.hot.decline();
} catch {
}
export {
  composeStories,
  composeStory,
  setProjectAnnotations,
  setup,
  vueProjectAnnotations
};
//# sourceMappingURL=@storybook_vue3.js.map
